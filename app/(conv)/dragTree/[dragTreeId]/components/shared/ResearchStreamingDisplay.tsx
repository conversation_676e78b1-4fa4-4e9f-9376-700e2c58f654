'use client'

import React, { useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { FiSearch, FiList } from 'react-icons/fi'
import {
  Conversation,
  ConversationContent,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import { Response } from '@/components/ai-elements/response'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'
import {
  Tool,
  ToolHeader,
  ToolContent,
  ToolInput,
  ToolOutput,
} from '@/components/ai-elements/tool'

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

type ResearchStreamingDisplayProps = {
  messages: any[]
  isStreaming: boolean
  className?: string
  onConvertToEditor?: (content: string) => void
}

/**
 * New streaming display component for OpenAI native web search
 * Shows reasoning steps and tool execution in real-time
 * Replaces the textarea phase with AI SDK elements
 */
export const ResearchStreamingDisplay: React.FC<
  ResearchStreamingDisplayProps
> = ({ messages, isStreaming, className, onConvertToEditor }) => {
  const [stepsModalOpen, setStepsModalOpen] = useState<boolean>(false)

  // Helpers to detect visible content
  const hasAssistantText = (m: any): boolean => {
    if (typeof m?.content === 'string' && m.content.trim()) return true
    if (Array.isArray(m?.content)) {
      const text = m.content
        .filter(
          (p: any) =>
            (p?.type === 'text' && typeof p?.text === 'string') ||
            (p?.type === 'text-delta' &&
              typeof (p as any)?.textDelta === 'string')
        )
        .map((p: any) =>
          p.type === 'text-delta' ? (p as any).textDelta : p.text
        )
        .join('')
        .trim()
      if (text) return true
    }
    if (Array.isArray(m?.parts)) {
      const text = m.parts
        .filter(
          (p: any) =>
            (p?.type === 'text' && typeof p?.text === 'string') ||
            (p?.type === 'text-delta' &&
              typeof (p as any)?.textDelta === 'string')
        )
        .map((p: any) =>
          p.type === 'text-delta' ? (p as any).textDelta : p.text
        )
        .join('')
        .trim()
      if (text) return true
    }
    return false
  }

  // Determine if final assistant output has actually started (text tokens present)
  const finalAssistantMessage = messages
    .filter(m => m.role === 'assistant')
    .pop()
  const finalStreamingStarted = Boolean(
    isStreaming &&
      finalAssistantMessage &&
      hasAssistantText(finalAssistantMessage)
  )

  // Extract reasoning and tool messages for the Steps modal
  const reasoningAndToolMessages = messages.filter((msg, index) => {
    // Always exclude the last assistant response from steps (final output)
    if (msg.role === 'assistant' && index === messages.length - 1) {
      return false
    }
    return msg.role === 'assistant' || 'toolInvocations' in msg
  })

  // Get the final assistant response for main display
  // Steps button should appear once final output begins streaming, or after it finishes
  const showStepsButton =
    (finalStreamingStarted ||
      (!isStreaming && reasoningAndToolMessages.length > 0)) &&
    reasoningAndToolMessages.length > 0

  const handleStepsClick = useCallback(() => {
    setStepsModalOpen(true)
  }, [])

  const handleConvertToEditor = useCallback(() => {
    if (finalAssistantMessage && onConvertToEditor) {
      // Extract text content from the final message
      let content = ''
      if (typeof finalAssistantMessage.content === 'string') {
        content = finalAssistantMessage.content
      } else if (Array.isArray(finalAssistantMessage.content)) {
        content = finalAssistantMessage.content
          .filter((part: any) => part.type === 'text')
          .map((part: any) => part.text)
          .join(' ')
      }
      onConvertToEditor(content)
    }
  }, [finalAssistantMessage, onConvertToEditor])

  return (
    <div className={cn('w-full', className)}>
      {/* Main streaming content area */}
      <div className="relative">
        <Conversation className="min-h-[200px]">
          <ConversationContent>
            {messages
              .filter((message, index) => {
                // Never show user messages in this research stream UI
                if (message.role === 'user') return false

                // While final output is streaming, keep only the final assistant message
                if (finalStreamingStarted) {
                  return (
                    message.role === 'assistant' &&
                    index === messages.length - 1 &&
                    hasAssistantText(message)
                  )
                }

                // Before final output: show only messages that have something to display
                const hasReasoning = Boolean(message.reasoning)
                const hasTools =
                  'toolInvocations' in message && message.toolInvocations
                const hasText =
                  message.role === 'assistant' && hasAssistantText(message)
                return Boolean(hasReasoning || hasTools || hasText)
              })
              .map((message, index) => (
                <Message
                  key={index}
                  from={message.role as 'user' | 'system' | 'assistant'}
                >
                  <MessageContent>
                    {/* Handle reasoning messages */}
                    {!finalStreamingStarted && message.reasoning && (
                      <Reasoning>
                        <ReasoningTrigger />
                        <ReasoningContent>{message.reasoning}</ReasoningContent>
                      </Reasoning>
                    )}

                    {/* Handle tool invocations */}
                    {!finalStreamingStarted &&
                      'toolInvocations' in message &&
                      message.toolInvocations && (
                        <div className="space-y-2">
                          {message.toolInvocations.map(
                            (invocation: any, toolIndex: number) => (
                              <Tool key={toolIndex}>
                                <ToolHeader
                                  type="tool-web_search"
                                  state="output-available"
                                  isExpandable={false}
                                />
                                <ToolContent>
                                  {invocation.args && (
                                    <ToolInput input={invocation.args} />
                                  )}
                                  {invocation.result && (
                                    <ToolOutput
                                      output={invocation.result}
                                      errorText={undefined}
                                    />
                                  )}
                                </ToolContent>
                              </Tool>
                            )
                          )}
                        </div>
                      )}

                    {/* Handle regular assistant messages */}
                    {message.role === 'assistant' &&
                      message.content &&
                      !message.reasoning &&
                      !('toolInvocations' in message) && (
                        <Response>
                          {typeof message.content === 'string'
                            ? message.content
                            : Array.isArray(message.content)
                              ? message.content
                                  .filter(
                                    (part: any) =>
                                      part.type === 'text' ||
                                      part.type === 'text-delta'
                                  )
                                  .map((part: any) =>
                                    part.type === 'text-delta'
                                      ? (part as any).textDelta
                                      : part.text
                                  )
                                  .join(' ')
                              : ''}
                        </Response>
                      )}
                  </MessageContent>
                </Message>
              ))}

            {/* Show loading indicator when streaming */}
            {isStreaming && !finalStreamingStarted && (
              <Message from="assistant">
                <MessageContent>
                  <div className="flex items-center gap-2 text-gray-500">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
                    <span>Researching...</span>
                  </div>
                </MessageContent>
              </Message>
            )}
          </ConversationContent>
        </Conversation>

        {/* Steps button - positioned at bottom right when not streaming */}
        {showStepsButton && (
          <div className="absolute bottom-2 right-2">
            <Dialog open={stepsModalOpen} onOpenChange={setStepsModalOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleStepsClick}
                  className="h-8 px-3 text-xs bg-white/80 backdrop-blur-sm"
                >
                  <FiList className="h-3 w-3 mr-1" />
                  Steps ({reasoningAndToolMessages.length})
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Research Steps & Reasoning</DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                  <Conversation>
                    <ConversationContent>
                      {reasoningAndToolMessages.map((message, index) => (
                        <Message
                          key={index}
                          from={message.role as 'user' | 'system' | 'assistant'}
                        >
                          <MessageContent>
                            {/* Show reasoning */}
                            {message.reasoning && (
                              <Reasoning defaultOpen={true}>
                                <ReasoningTrigger />
                                <ReasoningContent>
                                  {message.reasoning}
                                </ReasoningContent>
                              </Reasoning>
                            )}

                            {/* Show tool invocations */}
                            {'toolInvocations' in message &&
                              message.toolInvocations && (
                                <div className="space-y-3">
                                  {message.toolInvocations.map(
                                    (invocation: any, toolIndex: number) => (
                                      <div
                                        key={toolIndex}
                                        className="border rounded-lg p-3"
                                      >
                                        <div className="flex items-center gap-2 mb-2">
                                          <FiSearch className="h-4 w-4" />
                                          <span className="font-medium text-sm">
                                            {invocation.toolName ===
                                            'web_search_preview'
                                              ? 'Web Search'
                                              : invocation.toolName}
                                          </span>
                                        </div>
                                        {invocation.args && (
                                          <div className="mb-2">
                                            <div className="text-xs text-gray-600 mb-1">
                                              Input:
                                            </div>
                                            <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                                              {JSON.stringify(
                                                invocation.args,
                                                null,
                                                2
                                              )}
                                            </pre>
                                          </div>
                                        )}
                                        {invocation.result && (
                                          <div>
                                            <div className="text-xs text-gray-600 mb-1">
                                              Output:
                                            </div>
                                            <div className="text-sm bg-blue-50 p-2 rounded max-h-32 overflow-y-auto">
                                              {typeof invocation.result ===
                                              'string'
                                                ? invocation.result
                                                : JSON.stringify(
                                                    invocation.result,
                                                    null,
                                                    2
                                                  )}
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    )
                                  )}
                                </div>
                              )}

                            {/* Show assistant content */}
                            {message.role === 'assistant' &&
                              message.content &&
                              !message.reasoning && (
                                <Response>
                                  {typeof message.content === 'string'
                                    ? message.content
                                    : Array.isArray(message.content)
                                      ? message.content
                                          .filter(
                                            (part: any) => part.type === 'text'
                                          )
                                          .map((part: any) => part.text)
                                          .join(' ')
                                      : ''}
                                </Response>
                              )}
                          </MessageContent>
                        </Message>
                      ))}
                    </ConversationContent>
                  </Conversation>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>

      {/* Convert to editor button when streaming is complete */}
      {!isStreaming && finalAssistantMessage && (
        <div className="mt-4 flex justify-center">
          <Button
            onClick={handleConvertToEditor}
            variant="outline"
            size="sm"
            className="text-xs"
          >
            Convert to Editor
          </Button>
        </div>
      )}
    </div>
  )
}
