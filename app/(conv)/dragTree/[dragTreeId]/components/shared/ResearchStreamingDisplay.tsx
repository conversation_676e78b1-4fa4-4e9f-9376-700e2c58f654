'use client'

import React, { useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { FiSearch, FiList } from 'react-icons/fi'
import {
  Conversation,
  ConversationContent,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import { Response } from '@/components/ai-elements/response'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'
import {
  Tool,
  ToolHeader,
  ToolContent,
  ToolInput,
  ToolOutput,
} from '@/components/ai-elements/tool'

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

type ResearchStreamingDisplayProps = {
  messages: any[]
  isStreaming: boolean
  className?: string
  onConvertToEditor?: (content: string) => void
}

/**
 * New streaming display component for OpenAI native web search
 * Shows reasoning steps and tool execution in real-time
 * Replaces the textarea phase with AI SDK elements
 *
 * Updated to handle parts-based message format:
 * - message.parts[] with {type: "reasoning", text: "..."}
 * - message.parts[] with {type: "tool-web_search_preview", ...}
 * - message.parts[] with {type: "text", text: "..."}
 */
export const ResearchStreamingDisplay: React.FC<
  ResearchStreamingDisplayProps
> = ({ messages, isStreaming, className, onConvertToEditor }) => {
  const [stepsModalOpen, setStepsModalOpen] = useState<boolean>(false)

  // Helper functions to work with parts-based message format
  const hasAssistantText = (m: any): boolean => {
    // Check for text parts in the parts array (new format)
    if (Array.isArray(m?.parts)) {
      const textParts = m.parts.filter(
        (p: any) =>
          p?.type === 'text' && typeof p?.text === 'string' && p.text.trim()
      )
      if (textParts.length > 0) return true
    }

    // Fallback to legacy formats
    if (typeof m?.content === 'string' && m.content.trim()) return true
    if (Array.isArray(m?.content)) {
      const text = m.content
        .filter(
          (p: any) =>
            (p?.type === 'text' && typeof p?.text === 'string') ||
            (p?.type === 'text-delta' &&
              typeof (p as any)?.textDelta === 'string')
        )
        .map((p: any) =>
          p.type === 'text-delta' ? (p as any).textDelta : p.text
        )
        .join('')
        .trim()
      if (text) return true
    }
    return false
  }

  // Helper to check if message has reasoning parts
  const hasReasoningParts = (m: any): boolean => {
    return (
      Array.isArray(m?.parts) &&
      m.parts.some((p: any) => p?.type === 'reasoning')
    )
  }

  // Helper to check if message has tool parts
  const hasToolParts = (m: any): boolean => {
    return (
      Array.isArray(m?.parts) &&
      m.parts.some((p: any) => p?.type?.startsWith('tool-'))
    )
  }

  // Helper to extract text content from parts
  const extractTextFromParts = useCallback((parts: any[]): string => {
    return parts
      .filter((p: any) => p?.type === 'text' && typeof p?.text === 'string')
      .map((p: any) => p.text)
      .join('')
  }, [])

  // Determine if final assistant output has actually started (text tokens present)
  const finalAssistantMessage = messages
    .filter(m => m.role === 'assistant')
    .pop()
  const finalStreamingStarted = Boolean(
    isStreaming &&
      finalAssistantMessage &&
      hasAssistantText(finalAssistantMessage)
  )

  // Extract reasoning and tool messages for the Steps modal
  // Look for messages with reasoning or tool parts, excluding final text-only responses
  const reasoningAndToolMessages = messages.filter((msg, index) => {
    // Always exclude the last assistant response from steps if it only has text (final output)
    if (msg.role === 'assistant' && index === messages.length - 1) {
      // Only exclude if it has text parts but no reasoning/tool parts
      if (
        hasAssistantText(msg) &&
        !hasReasoningParts(msg) &&
        !hasToolParts(msg)
      ) {
        return false
      }
    }

    // Include messages that have reasoning or tool parts
    return (
      msg.role === 'assistant' && (hasReasoningParts(msg) || hasToolParts(msg))
    )
  })

  // Get the final assistant response for main display
  // Steps button should appear once final output begins streaming, or after it finishes
  const showStepsButton =
    (finalStreamingStarted ||
      (!isStreaming && reasoningAndToolMessages.length > 0)) &&
    reasoningAndToolMessages.length > 0

  const handleStepsClick = useCallback(() => {
    setStepsModalOpen(true)
  }, [])

  const handleConvertToEditor = useCallback(() => {
    if (finalAssistantMessage && onConvertToEditor) {
      // Extract text content from the final message
      let content = ''

      // Handle parts-based format first
      if (Array.isArray(finalAssistantMessage.parts)) {
        content = extractTextFromParts(finalAssistantMessage.parts)
      }
      // Fallback to legacy formats
      else if (typeof finalAssistantMessage.content === 'string') {
        content = finalAssistantMessage.content
      } else if (Array.isArray(finalAssistantMessage.content)) {
        content = finalAssistantMessage.content
          .filter((part: any) => part.type === 'text')
          .map((part: any) => part.text)
          .join(' ')
      }

      onConvertToEditor(content)
    }
  }, [finalAssistantMessage, onConvertToEditor, extractTextFromParts])

  return (
    <div className={cn('w-full', className)}>
      {/* Main streaming content area */}
      <div className="relative">
        <Conversation className="min-h-[200px]">
          <ConversationContent>
            {messages
              .filter((message, index) => {
                // Never show user messages in this research stream UI
                if (message.role === 'user') return false

                // While final output is streaming, keep only the final assistant message
                if (finalStreamingStarted) {
                  return (
                    message.role === 'assistant' &&
                    index === messages.length - 1 &&
                    hasAssistantText(message)
                  )
                }

                // Before final output: show only messages that have something to display
                const hasReasoning = hasReasoningParts(message)
                const hasTools = hasToolParts(message)
                const hasText =
                  message.role === 'assistant' && hasAssistantText(message)
                return Boolean(hasReasoning || hasTools || hasText)
              })
              .map((message, index) => (
                <Message
                  key={index}
                  from={message.role as 'user' | 'system' | 'assistant'}
                >
                  <MessageContent>
                    {/* Handle parts-based message format */}
                    {Array.isArray(message.parts) &&
                      message.parts.map((part: any, partIndex: number) => {
                        // Handle reasoning parts
                        if (
                          !finalStreamingStarted &&
                          part.type === 'reasoning'
                        ) {
                          return (
                            <Reasoning key={partIndex}>
                              <ReasoningTrigger />
                              <ReasoningContent>{part.text}</ReasoningContent>
                            </Reasoning>
                          )
                        }

                        // Handle tool parts
                        if (
                          !finalStreamingStarted &&
                          part.type?.startsWith('tool-')
                        ) {
                          return (
                            <Tool key={partIndex}>
                              <ToolHeader
                                type={part.type}
                                state={part.state || 'output-available'}
                                isExpandable={false}
                              />
                              <ToolContent>
                                {/* Show searching indicator for web search tools */}
                                {part.type === 'tool-web_search_preview' &&
                                  part.state !== 'output-available' &&
                                  !part.output && (
                                    <div className="flex items-center gap-2 text-gray-500 text-sm py-2">
                                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-500"></div>
                                      <span>Searching...</span>
                                    </div>
                                  )}
                                {part.input && <ToolInput input={part.input} />}
                                {part.output && (
                                  <ToolOutput
                                    output={part.output}
                                    errorText={undefined}
                                  />
                                )}
                              </ToolContent>
                            </Tool>
                          )
                        }

                        // Handle text parts (final output)
                        if (part.type === 'text' && part.text) {
                          return (
                            <Response key={partIndex}>{part.text}</Response>
                          )
                        }

                        return null
                      })}

                    {/* Fallback for legacy message formats */}
                    {!Array.isArray(message.parts) &&
                      message.role === 'assistant' &&
                      message.content && (
                        <Response>
                          {typeof message.content === 'string'
                            ? message.content
                            : Array.isArray(message.content)
                              ? message.content
                                  .filter(
                                    (part: any) =>
                                      part.type === 'text' ||
                                      part.type === 'text-delta'
                                  )
                                  .map((part: any) =>
                                    part.type === 'text-delta'
                                      ? (part as any).textDelta
                                      : part.text
                                  )
                                  .join(' ')
                              : ''}
                        </Response>
                      )}
                  </MessageContent>
                </Message>
              ))}

            {/* Show loading indicator when streaming */}
            {isStreaming && !finalStreamingStarted && (
              <Message from="assistant">
                <MessageContent>
                  <div className="flex items-center gap-2 text-gray-500">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
                    <span>Researching...</span>
                  </div>
                </MessageContent>
              </Message>
            )}
          </ConversationContent>
        </Conversation>

        {/* Steps button - positioned at bottom right when not streaming */}
        {showStepsButton && (
          <div className="absolute bottom-2 right-2">
            <Dialog open={stepsModalOpen} onOpenChange={setStepsModalOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleStepsClick}
                  className="h-8 px-3 text-xs bg-white/80 backdrop-blur-sm"
                >
                  <FiList className="h-3 w-3 mr-1" />
                  Steps ({reasoningAndToolMessages.length})
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Research Steps & Reasoning</DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                  <Conversation>
                    <ConversationContent>
                      {reasoningAndToolMessages.map((message, index) => (
                        <Message
                          key={index}
                          from={message.role as 'user' | 'system' | 'assistant'}
                        >
                          <MessageContent>
                            {/* Handle parts-based format for steps modal */}
                            {Array.isArray(message.parts) &&
                              message.parts.map(
                                (part: any, partIndex: number) => {
                                  // Show reasoning parts
                                  if (part.type === 'reasoning') {
                                    return (
                                      <Reasoning
                                        key={partIndex}
                                        defaultOpen={true}
                                      >
                                        <ReasoningTrigger />
                                        <ReasoningContent>
                                          {part.text}
                                        </ReasoningContent>
                                      </Reasoning>
                                    )
                                  }

                                  // Show tool parts with detailed display
                                  if (part.type?.startsWith('tool-')) {
                                    return (
                                      <div
                                        key={partIndex}
                                        className="border rounded-lg p-3 mb-3"
                                      >
                                        <div className="flex items-center gap-2 mb-2">
                                          <FiSearch className="h-4 w-4" />
                                          <span className="font-medium text-sm">
                                            {part.type ===
                                            'tool-web_search_preview'
                                              ? 'Web Search'
                                              : part.type
                                                  .replace('tool-', '')
                                                  .replace('_', ' ')}
                                          </span>
                                          {part.state && (
                                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                              {part.state}
                                            </span>
                                          )}
                                        </div>
                                        {part.input &&
                                          Object.keys(part.input).length >
                                            0 && (
                                            <div className="mb-2">
                                              <div className="text-xs text-gray-600 mb-1">
                                                Input:
                                              </div>
                                              <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                                                {JSON.stringify(
                                                  part.input,
                                                  null,
                                                  2
                                                )}
                                              </pre>
                                            </div>
                                          )}
                                        {part.output && (
                                          <div>
                                            <div className="text-xs text-gray-600 mb-1">
                                              Output:
                                            </div>
                                            <div className="text-sm bg-blue-50 p-2 rounded max-h-32 overflow-y-auto">
                                              {typeof part.output === 'string'
                                                ? part.output
                                                : JSON.stringify(
                                                    part.output,
                                                    null,
                                                    2
                                                  )}
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    )
                                  }

                                  return null
                                }
                              )}

                            {/* Fallback for legacy format */}
                            {!Array.isArray(message.parts) && (
                              <>
                                {/* Legacy reasoning */}
                                {(message as any).reasoning && (
                                  <Reasoning defaultOpen={true}>
                                    <ReasoningTrigger />
                                    <ReasoningContent>
                                      {(message as any).reasoning}
                                    </ReasoningContent>
                                  </Reasoning>
                                )}

                                {/* Legacy tool invocations */}
                                {'toolInvocations' in message &&
                                  (message as any).toolInvocations && (
                                    <div className="space-y-3">
                                      {(message as any).toolInvocations.map(
                                        (
                                          invocation: any,
                                          toolIndex: number
                                        ) => (
                                          <div
                                            key={toolIndex}
                                            className="border rounded-lg p-3"
                                          >
                                            <div className="flex items-center gap-2 mb-2">
                                              <FiSearch className="h-4 w-4" />
                                              <span className="font-medium text-sm">
                                                {invocation.toolName ===
                                                'web_search_preview'
                                                  ? 'Web Search'
                                                  : invocation.toolName}
                                              </span>
                                            </div>
                                            {invocation.args && (
                                              <div className="mb-2">
                                                <div className="text-xs text-gray-600 mb-1">
                                                  Input:
                                                </div>
                                                <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                                                  {JSON.stringify(
                                                    invocation.args,
                                                    null,
                                                    2
                                                  )}
                                                </pre>
                                              </div>
                                            )}
                                            {invocation.result && (
                                              <div>
                                                <div className="text-xs text-gray-600 mb-1">
                                                  Output:
                                                </div>
                                                <div className="text-sm bg-blue-50 p-2 rounded max-h-32 overflow-y-auto">
                                                  {typeof invocation.result ===
                                                  'string'
                                                    ? invocation.result
                                                    : JSON.stringify(
                                                        invocation.result,
                                                        null,
                                                        2
                                                      )}
                                                </div>
                                              </div>
                                            )}
                                          </div>
                                        )
                                      )}
                                    </div>
                                  )}
                              </>
                            )}
                          </MessageContent>
                        </Message>
                      ))}
                    </ConversationContent>
                  </Conversation>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>

      {/* Convert to editor button when streaming is complete */}
      {!isStreaming && finalAssistantMessage && (
        <div className="mt-4 flex justify-center">
          <Button
            onClick={handleConvertToEditor}
            variant="outline"
            size="sm"
            className="text-xs"
          >
            Convert to Editor
          </Button>
        </div>
      )}
    </div>
  )
}
